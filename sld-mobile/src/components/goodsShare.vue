<template>
	<!-- 分享 -->
	<view class="share">
		<!-- 分享弹框 start -->
		<view class="share_model flex_column_end_center" v-if="share_model" @touchmove.stop.prevent="moveHandle">
			<view class="share_model_list">
				<!-- #ifdef H5 -->
				<view class="share_model_pre" @tap.stop="sldShareBrower(1)" v-if="isWeiXinBrower">
					<image :src="imgUrl + 'goods_detail/wx_share.png'" mode="aspectFit"></image>
					<text>{{ $L('微信好友') }}</text>
				</view>
				<view class="share_model_pre" @tap.stop="sldShareBrower(2)" v-if="isWeiXinBrower">
					<image :src="imgUrl + 'goods_detail/wechat_moments.png'" mode="aspectFit"></image>
					<text>{{ $L('微信朋友圈') }}</text>
				</view>
				<!-- #endif -->
				<!-- wx-1-start -->
				<!-- #ifdef MP-WEIXIN -->
				<button class="share_model_pre" open-type="share">
					<image :src="imgUrl + 'goods_detail/wx_share.png'" mode="aspectFit"></image>
					<text>{{ $L('微信好友') }}</text>
				</button>
				<!-- #endif -->

				<view class="share_model_pre">
					<image :src="imgUrl + 'goods_detail/poster.png'" mode="aspectFit" @click="getPoster"></image>
					<text>{{ $L('生成海报') }}</text>
				</view>
			</view>
			<view class="share_model_close" @click="closeShareModel">
				<image :src="imgUrl + 'goods_detail/share_close.png'" mode="aspectFit"></image>
			</view>
		</view>
		<!-- 分享弹框 end -->

		<!-- 生成海报  start-->
		<view class="poster" v-if="poster" @touchmove.stop.prevent="moveHandle">
			<!-- 分享海报弹框 start -->
			<view class="share_model" :class="{ poster_share_model: poster }">
				<sldPoster ref="sldPoster" :info="posterInfo" @cachePoster="cachePoster"></sldPoster>

				<view class="share_model_list">
					<!-- wx-2-start -->
					<!-- #ifdef MP -->
					<view class="share_model_pre" @tap.stop="downloadPoster">
						<image :src="imgUrl + 'goods_detail/poster.png'" mode="aspectFit"></image>
						<text>{{ $L('下载海报') }}</text>
					</view>
					<!-- #endif -->
					<!-- wx-2-end -->
				</view>
				<!-- #ifndef H5 -->
				<view class="share_model_close" @click="closeShareModel">
					<image :src="imgUrl + 'goods_detail/share_close.png'" mode="aspectFit"></image>
				</view>
				<!-- #endif -->
				<!-- #ifdef H5 -->
				<image :src="imgUrl + 'goods_detail/poster_share.png'" mode="aspectFit" class="poster_share_img">
				</image>
				<image :src="imgUrl + 'goods_detail/poster_share_close.png'" mode="aspectFit" class="poster_share_close"
					@click.stop="closePoster"></image>
				<!-- #endif -->
			</view>
			<!-- 分享海报弹框 end -->
		</view>
		<!-- 生成海报  end-->


		<!-- 微信浏览器分享提示  start-->
		<view class="wx_brower_share_mask" v-if="showWeiXinBrowerTip">
			<view class="wx_brower_share_top_wrap">
				<image :src="imgUrl + 'wx_share_tip.png'" mode="widthFix" @tap="closeShareModel"
					class="wx_brower_share_img"></image>
			</view>
		</view>
		<!-- 微信浏览器分享提示  end-->
		<!-- app-start -->
		<view class="smegma" v-if="perm"></view>
		<view class="frame" v-if="perm">
			<view class="fra_title">相册权限说明</view>
			<view class="">
				申请相册权限以便您保存商品海报到手机相册。拒绝或取消授权不影响使用其他服务。
			</view>
		</view>
		<!-- app-end -->
	</view>
	<!-- 分享 -->
</template>

<script>
import sldPoster from '@/components/sld_poster/poster.vue';
import { mapState } from 'vuex';
// app-start
import { sgCheckLogin } from '@/utils/common.js';
import permision from "@/utils/permission";
// app-end
export default {
	components: {
		sldPoster
	},

	props: {
		type: {
			type: String,
			default: 'normal'
		}
	},

	data() {
		return {
			share_model: false, //分享弹框
			showWeiXinBrowerTip: false,
			isWeiXinBrower: false,
			imgUrl: process.env.VUE_APP_IMG_URL,
			goodsData: {},
			poster: false,
			// app-start
			systemInfo: null,  //手机端平台类型
			perm: false,
			// app-end
			priceRange: null, // 价格区间 {min: number, max: number}
		}
	},

	computed: {
		...mapState(['userCenterData', 'hasLogin']),
		posterInfo() {
			let {
				goodsName,
				defaultProduct
			} = this.goodsData

			let url = this.$Route.path
			Object.keys(this.$Route.query).forEach(key => {
				if (url.indexOf('?') > -1) {
					url += `&${key}=${this.$Route.query[key]}`
				} else {
					url += `?${key}=${this.$Route.query[key]}`
				}
			})
			let info = {
				name: goodsName,
				brief: '', // 不显示商品简介
				url: process.env.VUE_APP_API_URL + url.substring(1)
			}

			console.log('用户数据:', this.userCenterData)
			// 添加用户头像和店铺名称
			if (this.userCenterData) {
				info.userAvatar = this.userCenterData.memberAvatar
				info.shopName = this.userCenterData.memberNickName || this.userCenterData.memberName || '-'
				console.log('设置头像:', info.userAvatar)
				console.log('设置店铺名:', info.shopName)
			} else {
				uni.showToast({
					title: '需要登录',
					success: () => {
						this.$Router.replace('/pages/public/login')
					}
				})
			}

			if (defaultProduct) {
				info.marketPrice = defaultProduct.marketPrice ? `¥${Number(defaultProduct.marketPrice).toFixed(2)}` : ''
				info.image = defaultProduct.goodsPics[0]

				if (this.type == 'point') {
					if (defaultProduct.cashPrice) {
						info.price = `${defaultProduct.integralPrice}积分+¥${Number(defaultProduct.cashPrice).toFixed(2)}`
					} else {
						info.price = `${defaultProduct.integralPrice}积分`
					}
				} else {
					// 使用价格区间或单个价格
					if (this.priceRange && this.priceRange.min !== this.priceRange.max) {
						info.price = `¥${Number(this.priceRange.min).toFixed(2)}-${Number(this.priceRange.max).toFixed(2)}`
					} else {
						info.price = '¥' + Number(defaultProduct.productPrice).toFixed(2)
					}
				}
			}

			return info
		}
	},
	mounted() {

		// #ifdef H5
		this.isWeiXinBrower = this.$isWeiXinBrower()
		// #endif 
	},
	onShow() {
		if (!this.hasLogin || !this.userCenterData) {
			uni.showToast({
				title: '需要登录',
				success: () => {
					this.$Router.replace('/pages/public/login')
				}
			})

		}
	},

	methods: {
		// app-start
		async requestAndroidPermission(permisionID) {
			if (this.systemInfo == 'android') {
				setTimeout(() => {
					this.perm = true;
				}, 100)
				var result = await permision.requestAndroidPermission(permisionID)
				var strStatus
				if (result == 1) {
					this.perm = false;
					this.saveImgAPP()
				} else if (result == 0) {
					this.perm = false;
					strStatus = "未获得授权"
				} else {
					this.perm = false;
					strStatus = "被永久拒绝权限"
					uni.showToast({
						title: '权限被永久拒绝,可以到手机设置里修改',
						icon: 'none',
						mask: true,
					})
				}
			} else {
				this.saveImgAPP()
			}

		},
		// app-end
		//浏览器分享
		sldShareBrower(type) {
			this.showWeiXinBrowerTip = true
			this.share_model = false
			this.$WXBrowserShareThen(type, {
				title: this.goodsData.goodsName,
				desc: this.goodsData.goodsBrief,
				link: this.goodsData.shareLink,
				imgUrl: this.goodsData.shareImage
			})
		},


		//关闭分享弹框
		closeShareModel() {
			this.share_model = false
			this.showWeiXinBrowerTip = false
			this.poster = false
		},
		//获取海报
		getPoster() {
			//wx-3-start
			// #ifdef MP-WEIXIN
			if (this.posterInfo.posterCache) {
				this.share_model = false
				this.showWeiXinBrowerTip = false
				this.poster = true
			} else {
				this.getSunCode()
			}
			// #endif
			//wx-3-end
			// #ifndef MP-WEIXIN
			this.share_model = false
			this.showWeiXinBrowerTip = false
			this.poster = true
			// #endif
		},

		//分享 type：分享类型 0 图文 2图片，scene 场景 WXSceneSession：分享朋友  WXSenceTimeline：分享朋友圈
		sldShare(type, scene) {
			let shareData = {};
			let _this = this
			let {
				goodsData,
				sharePoster
			} = this;
			if (type == 0) {
				shareData.href = goodsData.shareLink;
				shareData.title = goodsData.goodsName;
				shareData.summary = goodsData.goodsBrief;
				shareData.imageUrl = goodsData.shareImage;
			} else if (type == 2) {
				shareData.imageUrl = this.posterInfo.posterCache;
			}
			this.$weiXinAppShare(type, scene, shareData);
			this.closeShareModel(); //关闭分享
		},

		//下载海报
		downloadPoster() {
			this.$refs.sldPoster.savePoster()
		},

		//APP端保存图片的方法
		saveImgAPP() {
			setTimeout(() => {
				this.perm = false;
			}, 100)
			this.$refs.sldPoster.savePoster()
		},

		moveHandle() {

		},


		async injectInfo(goodsInfo) {
			if (!sgCheckLogin()) {
				console.warn('unlogin')
				return;
			}
			this.goodsData = goodsInfo

			// 通过接口获取价格区间
			await this.getPriceRangeFromApi(goodsInfo)

			this.share_model = true
		},

		// 通过接口获取价格区间
		async getPriceRangeFromApi(goodsData) {
			try {
				const response = await this.$request({
					url: 'v3/goods/front/goods/details2',
					data: {
						productId: goodsData.defaultProduct.productId,
						goodsId: goodsData.goodsId,
						source: this.$Route.query.source || ''
					}
				})

				if (response.state === 200 && response.data) {
					const { commissionRate, minPrice, maxPrice } = response.data

					// 如果commissionRate不为空则代表是按【佣金比例】，使用接口返回的价格区间
					if (commissionRate !== undefined && commissionRate !== null && commissionRate !== '') {
						if (minPrice !== undefined && maxPrice !== undefined) {
							this.priceRange = {
								min: Number(minPrice),
								max: Number(maxPrice)
							}
							console.log('从接口获取价格区间:', this.priceRange, '佣金比例:', commissionRate)
						} else {
							console.log('接口返回的价格区间数据不完整')
							this.priceRange = null
						}
					} else {
						console.log('commissionRate为空，不使用价格区间')
						this.priceRange = null
					}
				} else {
					console.log('获取商品详情接口调用失败:', response.msg)
					this.priceRange = null
				}
			} catch (error) {
				console.log('获取价格区间失败:', error)
				this.priceRange = null
			}
		},

		// 计算价格区间（备用方法，保留以防需要回退）
		async calculatePriceRange(goodsData) {
			try {
				const prices = await this.getAllSkuPrices(goodsData)
				if (prices.length > 0) {
					const numPrices = prices.map(p => Number(p)).filter(p => !isNaN(p))
					if (numPrices.length > 0) {
						this.priceRange = {
							min: Math.min(...numPrices),
							max: Math.max(...numPrices)
						}
						console.log('价格区间:', this.priceRange)
					}
				}
			} catch (error) {
				console.log('获取价格区间失败:', error)
				this.priceRange = null
			}
		},

		// 获取所有SKU价格
		async getAllSkuPrices(goodsData) {
			// 如果没有规格或规格为空，返回默认价格
			if (!goodsData.specs || goodsData.specs.length === 0) {
				return [goodsData.defaultProduct.productPrice]
			}
			
			const combinations = this.generateSpecCombinations(goodsData.specs)
			
			console.log('规格组合数量:', combinations.length)
			
			// 并行请求所有规格组合的价格
			const requests = combinations.map(combination => 
				this.$request({
					url: 'v3/goods/front/goods/productInfo',
					data: {
						goodsId: goodsData.goodsId,
						specValueIds: combination.join(',')
					}
				}).then(result => {
					if (result.state === 200 && result.data.defaultProduct) {
						return result.data.defaultProduct.productPrice
					}
					return null
				}).catch(error => {
					console.log('获取规格价格失败:', error)
					return null
				})
			)
			
			const results = await Promise.all(requests)
			const validPrices = results.filter(price => price !== null)
			
			return validPrices.length > 0 ? validPrices : [goodsData.defaultProduct.productPrice]
		},

		// 生成所有规格组合
		generateSpecCombinations(specs) {
			if (specs.length === 0) return [[]]
			
			const result = []
			const generate = (index, current) => {
				if (index === specs.length) {
					result.push([...current])
					return
				}
				
				const availableValues = specs[index].specValueList.filter(
					specValue => specValue.checkState !== '3' // 排除禁用规格
				)
				
				for (const specValue of availableValues) {
					current.push(specValue.specValueId)
					generate(index + 1, current)
					current.pop()
				}
			}
			
			generate(0, [])
			return result
		},


		getSunCode() {
			uni.showLoading({
				title: '加载中...'
			})
			let { defaultProduct } = this.goodsData
			this.$request({
				url: 'v3/goods/common/sunCode',
				data: {
					productId: defaultProduct.productId || defaultProduct.integralProductId,
					goodsId: this.goodsData.goodsId || 0,
					page: this.$Route.path.substring(1),
				}
			}).then(res => {
				if (res.state == 200) {
					this.posterInfo.qrcode = `data:image/png;base64,${res.data}`
					this.share_model = false
					this.showWeiXinBrowerTip = false
					this.poster = true
				} else {
					uni.hideLoading()
					setTimeout(() => {
						this.$api.msg(res.msg)
					}, 500)
				}
			})
		},

		//缓存poster组件生成的海报
		cachePoster(url) {
			this.posterInfo.posterCache = url
		},


		//关闭海报
		closePoster() {
			this.poster = false
		},

	}
}
</script>

<style lang="scss">
.share_model {
	width: 750rpx;
	height: 100%;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	margin: 0 auto;
	background: rgba(0, 0, 0, 0.6);
	z-index: 100;
}

.share_model_list {
	display: flex;
	justify-content: space-around;
	padding: 0 50rpx;
	box-sizing: border-box;

	z-index: 110;
	width: 750rpx;

	.share_model_pre {
		display: flex;
		flex-direction: column;
		align-items: center;
		background: transparent;
		border-radius: 0;
		height: auto;
		line-height: auto;
		margin: 20rpx 0;

		&::after {
			border-width: 0;
		}

		image {
			width: 105rpx;
			height: 105rpx;
		}

		text {
			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			line-height: 36rpx;
			margin-top: 30rpx;
		}
	}
}

.share_model_close {
	width: 46rpx;
	height: 46rpx;

	z-index: 110;
	left: 0;
	right: 0;
	margin: 0 auto;
	margin-bottom: 60rpx;

	image {
		width: 46rpx;
		height: 46rpx;
	}
}

.poster {
	width: 750rpx;
	height: 100%;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	margin: 0 auto;
	background: rgba(0, 0, 0, 0.6);
	z-index: 100;

	.poster_share_img {
		width: 390rpx;
		height: 90rpx;
		margin: 72rpx 0 22rpx;
	}

	.poster_share_close {
		width: 49rpx;
		height: 49rpx;
	}

	.poster_share_model {
		width: 750rpx;
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		padding-top: 100rpx;

		.poster_share_img {
			width: 390rpx;
			height: 90rpx;
			margin: 72rpx 0 22rpx;
		}

		.poster_share_close {
			width: 49rpx;
			height: 49rpx;
		}
	}
}

button {
	padding: 0;
	margin: 0;
}

/* app-start */
.smegma {
	position: fixed;
	top: 0;
	left: 0;
	height: 100vh;
	width: 100vw;
	opacity: .8;
}

.frame {
	position: fixed;
	top: 0;



	left: 0;
	padding: 20rpx 20rpx;
	z-index: 10000;
	width: 100vw;
	line-height: 50rpx;
	background-color: #fff;
	border-radius: 10rpx;
}

.fra_title {
	font-weight: 700;
	font-size: 35rpx;
	margin-bottom: 10rpx;
}

/* app-end */
</style>