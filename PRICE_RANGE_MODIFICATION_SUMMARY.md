# 价格区间计算方式修改总结

## 修改概述
将 `goodsShare.vue` 组件中的价格区间计算方式从前端计算改为通过接口直接获取。

## 修改的文件
- `sld-mobile/src/components/goodsShare.vue`

## 具体修改内容

### 1. 修改 `injectInfo` 方法
**位置**: 第311-322行
**修改前**: 调用 `await this.calculatePriceRange(goodsInfo)`
**修改后**: 调用 `await this.getPriceRangeFromApi(goodsInfo)`

### 2. 新增 `getPriceRangeFromApi` 方法
**位置**: 第324-363行
**功能**: 
- 调用 `/v3/goods/front/goods/details2` 接口
- 检查 `commissionRate` 字段是否不为空
- 如果 `commissionRate` 不为空，则使用接口返回的 `minPrice` 和 `maxPrice`
- 设置 `this.priceRange = {min: Number(minPrice), max: Number(maxPrice)}`
- 如果 `commissionRate` 为空或接口调用失败，则设置 `this.priceRange = null`

### 3. 保留原有方法
**说明**: 保留了原有的 `calculatePriceRange`、`getAllSkuPrices` 和 `generateSpecCombinations` 方法作为备用，以防需要回退。

## 接口参数
调用 `/v3/goods/front/goods/details2` 接口时传递的参数：
- `productId`: 从 `goodsData.defaultProduct.productId` 获取
- `goodsId`: 从 `goodsData.goodsId` 获取  
- `source`: 从 `this.$Route.query.source` 获取，默认为空字符串

## 接口返回数据处理
- 检查 `response.state === 200` 确保接口调用成功
- 从 `response.data` 中提取 `commissionRate`、`minPrice`、`maxPrice` 字段
- 只有当 `commissionRate` 不为空时才使用价格区间数据
- 确保 `minPrice` 和 `maxPrice` 都存在才设置价格区间

## 价格显示逻辑
**位置**: `posterInfo` 计算属性（第174-180行）
**逻辑**: 
```javascript
// 使用价格区间或单个价格
if (this.priceRange && this.priceRange.min !== this.priceRange.max) {
    info.price = `¥${Number(this.priceRange.min).toFixed(2)}-${Number(this.priceRange.max).toFixed(2)}`
} else {
    info.price = '¥' + Number(defaultProduct.productPrice).toFixed(2)
}
```

## 海报绘制逻辑
**位置**: `poster.vue` 文件的 `drawText_price` 方法（第297-314行）
**说明**: 已有的海报绘制逻辑能够正确处理价格区间格式（如：¥19.9-39.9），无需修改。

## 错误处理
- 接口调用失败时设置 `priceRange = null`
- 数据不完整时设置 `priceRange = null`
- 添加了详细的 console.log 用于调试

## 兼容性
- 保持了原有的数据结构和接口
- `posterInfo` 计算属性的逻辑无需修改
- 海报绘制逻辑无需修改
- 如果新接口有问题，可以快速回退到原有的前端计算方式

## 测试建议
1. 测试有 `commissionRate` 的商品，验证价格区间是否正确显示
2. 测试没有 `commissionRate` 的商品，验证是否显示单个价格
3. 测试接口调用失败的情况，验证错误处理是否正确
4. 测试生成的海报中价格区间显示是否正确
